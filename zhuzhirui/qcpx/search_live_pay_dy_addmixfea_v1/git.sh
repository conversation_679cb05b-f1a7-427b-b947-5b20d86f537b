#!/bin/bash

# 脚本会在任何命令失败时停止执行
set -e

# 1. 添加所有变更
echo "=> Staging all changes..."
git add .

# 2. 检查是否有变更需要提交
if git diff --staged --quiet; then
    echo "=> No changes to commit, skipping commit step..."
else
    echo "=> Committing with message 'a'..."
    git commit -m "a"
fi

# 3. 检查并处理未暂存的更改
if ! git diff --quiet; then
    echo "=> Found unstaged changes, stashing them..."
    git stash push -m "auto-stash before pull"
    STASHED=true
else
    STASHED=false
fi

# 4. 拉取远程master分支并变基
echo "=> Pulling with rebase from origin/master..."
git pull origin master --rebase

# 5. 如果之前有stash，恢复它们
if [ "$STASHED" = true ]; then
    echo "=> Restoring stashed changes..."
    git stash pop
fi

# 6. 推送到远程master分支
echo "=> Pushing to origin/master..."
git push origin master

echo "=> Done!"
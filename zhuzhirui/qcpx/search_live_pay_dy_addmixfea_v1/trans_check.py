import os
# import copy
# import numpy as np
import time
import logging
# import warnings
# from setproctitle import setproctitle
import kai.tensorflow as kai
from kai.tensorflow.utils import log_helper
import tensorflow.compat.v1 as tf

tf.disable_v2_behavior()
logger = log_helper.get_logger(
    __name__, logging.INFO)


def runtime_main():
    """
    第一步: 导入kai，并初始化kai，从环境变量中拿到分布式各个节点的角色信息
    """
    logger.info("进入 runtime_main")
    kai.init()
    logger.info("kai-2.0 框架初始化成功")

    """
    第二步: 用户定义配置，或从已有配置文件解析
    """
    kai_config = kai.Config()
    kai_config.deserialize("./kai_v2_config.yaml")

    logger.info("kai-2.0 配置加载成功")

    """
    第三步: 用户定义模型结构
    """
    trans_origin = "python"
    if trans_origin == "cpp":
        import kai_v2_model
    elif trans_origin == "python":
        from kai_v2_model import UserModel
        model = UserModel()
        model_def = model.kai_v2_model_def()
    else:
        raise ValueError("直接使用模板，如果您是新用户，请自行修改第三步的模型定义方式，以及第五步中kai.start()的参数")
    logger.info("kai-2.0 模型加载成功")

    """
    第四步: 编译，配置检查，并生成dnn_plugin/reader.json等文件(dragonfly)
    第五步: 初始化运行期各个模块
    """

    if trans_origin == "cpp":
        kai.start(kai_config=kai_config)
    elif trans_origin == "python":
        kai.start(kai_config=kai_config, optimizer=model_def.get(
            'optimizer'), metrics=model_def.get('metrics'))

    logger.info("kai-2.0 组网成功")
    kai.shutdown()


if __name__ == '__main__':
    runtime_main()

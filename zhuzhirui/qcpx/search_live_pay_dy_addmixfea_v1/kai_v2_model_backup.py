import kai.tensorflow as kai
MODEL_TRANS_ORIGIN='python'
from kai.tensorflow.config.ad_config.ktrain.klearn_utils import data_ops
from kai.tensorflow.config.ad_config.ktrain.AUC import auc as auc_eval
import logging
LOG_FORMAT = "%(asctime)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s] - %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)
logger = logging.getLogger(__name__)
from tensorflow.python.ops import math_ops
import tensorflow as tf
#import tensorflow.compat.v1 as tf
import threading
from datetime import datetime
import numpy as np
import time
import sys
import codecs
import json
from datetime import timedelta
from tensorflow.python.ops import variables
import math
import os
from functools import partial
from tensorflow.python.tools.inspect_checkpoint import print_tensors_in_checkpoint_file

def dense_load_handle(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option): 
    ''' 
    https://docs.corp.kuaishou.com/k/home/<USER>/fcAAXcP_sb-h0_8v1lEr7wIqa#section=h.jitvgok6c7vl
    - 参数一：warmup_weight，从base的model加载得到的weight，key为参数名，value为numpy形式的参数值 。 
    - 参数二：warmup_extra，从base的model加载得到的extra(optimizer 依赖参数)，key为参数名，value为numpy形式的参数值
    - 参数三：ps_weight，从参数服务器上拉取的weight，key为参数名，value为numpy形式的参数值
    - 参数四：ps_extra，从参数服务器上拉取的extra，key为参数名，value为numpy形式的参数值
    - 参数五：tf_weight，tensorflow本地通过初始化op生成的weight，key为参数名，value为numpy形式的参数值
    - 参数六：load_option，kai.load()的配置，包含加载参数的地址，加载模式等信息
    - 返回值一：weight(dict), 最终确定的weight组合，key为参数名，value为numpy形式的参数值
    - 返回值二：extra(dict), 最终确定的extra组合，key为参数名，value为numpy形式的参数值
    - 都是这种格式： {weight_name1 : np_array, weight_name2 : np_array, ... }
    '''

    import numpy as np

    weight = None
    extra = None
    dense_variable_nums = len(tf_weight)


    '''
    a1 = 310*16
    a2  = 48+64+32+64
    b1  = 16 * 90
    b2  = 0
    '''
    # a1 = 310*16
    # a2 = 90*16
    # a3 = 16*20
    # b1 = 48+64+32+64
    # c1 = 310
    # c2 = 20 
    # c3 = 90
    a1 = 369*16
    a2 = 0
    b1 = 1187
    b2 = 1
    
# stdout 加载的 dense variable(share_bottom_layer_0/w:0) size ((6995, 512) vs (7155, 512)) 不匹配: --> Padding
# stdout 加载的 dense variable extra(share_bottom_layer_0/w:0) size ((3581440,) vs (3663360,)) 不匹配: --> Padding
    if warmup_weight is not None and len(warmup_weight) > 0:
        for var_name in list(warmup_weight):
            if var_name not in tf_weight: # 表示参数存在base模型，但新模型没有。即【删除参数】
                print("加载的 dense variable({}) 在运行时不存在，其值被忽略。".format(var_name))
                del warmup_weight[var_name]
                del warmup_extra[var_name]        

            elif warmup_weight[var_name].size != tf_weight[var_name].size: # base模型的参数维度和新模型不一样，即此参数被修改，需要额外处理。即【修改参数】
                if False:
                    print("加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                    del warmup_weight[var_name]
                    del warmup_extra[var_name]
                else:
                    print("加载的 dense variable({}) size ({} vs {}) 不匹配: --> Padding".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                    if var_name in ('share_bottom_layer_0/w:0'):

                        # warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:a1, :],   # base sparse wise
                        #                                           tf_weight[var_name][a1:a1+a2, :],
                        #                                           warmup_weight[var_name][a1:, :],   # base dense wise
                        #                                           tf_weight[var_name][a1+a2+b1:, :]                                                    
                        # ], axis=0)
                        warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:7091, :],
                                                                 tf_weight[var_name][7091:7092, :]                                                                    
                        ], axis=0)

                        assert warmup_weight[var_name].size == tf_weight[var_name].size, "dense variable({}) size ({} vs {}) 不匹配".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape)

                    # elif var_name in (
                    #                   'LayerNorm/beta',
                    #                   'LayerNorm/gamma'
                    #                  ):

                    #     warmup_weight[var_name] = np.concatenate([warmup_weight[var_name][:a1],   # base sparse wise
                    #                                               tf_weight[var_name][a1:a1+a2],
                    #                                               warmup_weight[var_name][a1:],    # base dense wise
                    #                                               tf_weight[var_name][a1+a2+b1:] 
                    #     ], axis=0)


                    #     assert warmup_weight[var_name].size == tf_weight[var_name].size, "dense variable({}) size ({} vs {}) 不匹配".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape)

                    
                    else: 
                        print("加载的 dense variable({}) size ({} vs {}) 不匹配: --> 其值被忽略".format(var_name, warmup_weight[var_name].shape, tf_weight[var_name].shape))
                        del warmup_weight[var_name]
                        del warmup_extra[var_name]


        weight = warmup_weight
    else:
        weight = tf_weight # 冷启动。用tf初始化。若用 weight=ps_weight，表示weight用ps初始化。



    if warmup_extra is not None and len(warmup_extra) > 0: # 
        for var_name in list(warmup_extra):
            if var_name not in ps_extra:
                print("加载的 dense variable extra({}) 在运行时不存在，其值被忽略。".format(var_name))  # noqa
                del warmup_extra[var_name]
            elif warmup_extra[var_name].size != ps_extra[var_name].size:
                if False:
                    print("加载的 dense variable extra({}) size ({} vs {}) 不匹配，其值被忽略".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                    del warmup_extra[var_name]

                else:
                    print("加载的 dense variable extra({}) size ({} vs {}) 不匹配: --> Padding".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                    if var_name in ('share_bottom_layer_0/w:0'):

                        # extra 是展开的
                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, 512))
                        ps_extra[var_name]     = ps_extra[var_name].reshape((-1, 512))

                        # warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:a1, :],   # base sparse wise
                        #                                           ps_extra[var_name][a1:a1+a2, :],
                        #                                           warmup_extra[var_name][a1:, :],    # base dense wise   
                        #                                           ps_extra[var_name][a1+a2+b1:, :]                                                                            
                        # ], axis=0)
                        warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:7091, :],
                                                                 ps_extra[var_name][7091:7092, :]                                                                                  
                        ], axis=0)

                        warmup_extra[var_name] = warmup_extra[var_name].reshape((-1, ))

                        assert warmup_extra[var_name].size == ps_extra[var_name].size, "dense variable extra({}) size ({} vs {}) 不匹配".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape)

                    # elif var_name in (
                    #                   'LayerNorm/beta',
                    #                   'LayerNorm/gamma'
                    #                  ):
                       
                    #     warmup_extra[var_name] = np.concatenate([warmup_extra[var_name][:a1],   # base sparse wise
                    #                                               ps_extra[var_name][a1:a1+a2],
                    #                                               warmup_extra[var_name][a1:],   # base dense wise 
                    #                                               ps_extra[var_name][a1+a2+b1:]
                    #     ], axis=0) 

                    #     assert warmup_extra[var_name].size == ps_extra[var_name].size, "dense variable extra({}) size ({} vs {}) 不匹配".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape)
                    else: 
                        print("加载的 dense variable extra({}) size ({} vs {}) 不匹配: --> 其值被忽略".format(var_name, warmup_extra[var_name].shape, ps_extra[var_name].shape))
                        del warmup_extra[var_name]
   

        extra = warmup_extra
    else:
        extra = ps_extra



    if len(weight) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增参数】
        for var_name, var in tf_weight.items():
            if var_name not in weight: # tf_weight 是新模型的所有w，而weight是前面一项项放入的。这里表示新模型有而旧模型没有。
                weight[var_name] = var # 表示：将新增的var_name 的数值arr赋予给 weight。 此处可改为自定义初始化后的arr
                # 或者使用 numpy 自己控制数值如何初始化
                # weight[var_name] = np.array()
            
    if len(extra) < dense_variable_nums: # 不存在base模型里，但存在新模型里，即【新增extra参数】
        for var_name, var in ps_extra.items():
            if var_name not in extra: # ps_extra是新模型的所有extra，而extra是前面一项项放入的
                extra[var_name] = var # 表示：将新增的var_name 的数值arr赋予给 extra，此处可改为自定义初始化后的arr


    assert len(weight) == dense_variable_nums
    assert len(extra) == dense_variable_nums

    return weight, extra # 返回让框架来赋值


# 还需要把save hdfs中的done list删 或者配置 force_load_init_model=true，才会走init的逻辑
kai.set_load_dense_func(dense_load_handle)


def load_bins(path): 
    with codecs.open(path, 'r') as f:
        line = f.readline().strip('\n')
        line = line.split(',')
        bins = [float(kk.split(':')[0]) for kk in line[0:-1]]
        bins_center_value = [float(kk.split(':')[1]) for kk in line[0:-1]] + [float(line[-1].split(':')[1])]
    return bins, bins_center_value

def get_label_by_bit(labels, bit, num): 
  val = 2**num - 1
  # (labels >> bit) & val 
  return tf.cast(tf.bitwise.bitwise_and(tf.bitwise.right_shift(labels, bit), val), tf.int32) #8
def get_lookup_tbl_with_neg(K, neg_weight, neg_label=1):   # 5, 1, 1  
    tbl = []
    for i in range(1 << (K)):
        items = []
        for j in range(K):
            if i & (1 << j) != 0:
                items.append(1.)
            else:
                items.append(0.)
        items.append(1.) # as sample weight
        tbl.append(items)
    tbl[neg_label][-1] = neg_weight # for neg sample
    return tbl
    for neg in neg_labels:
        tbl[neg][-1] = neg_weight  # for neg sample
    # tbl[neg_labels][-1] = neg_weight
    return tbl

class UpdateOpsHook(tf.train.SessionRunHook):
    """Hook to execute all `update_ops` from tf.GraphKeys.UPDATE_OPS before each run.
    One needs to call `update_ops` to see metric values during training."""
    def __init__(self, klearn_conf, klearn_ops):
        # Get all update_ops for (streaming) metrics, which are added
        # into `tf.GraphKeys.UPDATE_OPS` during creation of the graph
        self._update_ops = tf.get_collection(tf.GraphKeys.UPDATE_OPS, scope='metrics')
    def begin(self):
        self._global_step_tensor = tf.train.get_global_step()
        if self._global_step_tensor is None:
            raise RuntimeError("Global step should be created to use UpdateOpsHook.")
    def before_run(self, run_context):
        # Run `update_ops`
        return tf.train.SessionRunArgs(fetches=self._update_ops)
def get_variable_on_cpu(var_name, shape, is_cpu_ps, initializer=None):
    if initializer is None:
        #initializer = tf.contrib.layers.xavier_initializer()
        initializer = tf.keras.initializers.glorot_normal()
    return data_ops.variable_on_cpu(var_name, shape,
                                 initializer=initializer,
                                 is_cpu_ps=is_cpu_ps)
class HashDnnModel(object):
    def __init__(self):
        klearn_ops = []
        klearn_conf = type('', (), {})()
        klearn_conf.embedding_size = 16
        klearn_conf.exclude_dense_set = set()
        klearn_conf.task_weights = '1.0,1.0,1.0'
        klearn_conf.user_name = 'tiantian06'
        klearn_conf.ps_hash = 0
        klearn_conf.item_type = 'FANS_TOP_LIVE@AD_DSP@FANS_TOP@NATIVE_AD'
        klearn_conf.mtl_task_num = '3'
        klearn_conf.input_dense_user_count = 6
        klearn_conf.use_bn = False
        klearn_conf.version = 2
        klearn_conf.input_sparse_user_count = 188
        klearn_conf.label_extractor = 'mtl_compatible_live_paycnt_label_extractor'
        klearn_conf.base_lr = 0.1
        klearn_conf.share_num = 1
        klearn_conf.hash_type = 'djb2_hash64'
        klearn_conf.dnn_net_size = '512,512,512,2'
        klearn_conf.input_dense_total_size = 597
        klearn_conf.input_sparse_total_size = 4224
        klearn_conf.decay = 0.0
        klearn_conf.l2 = 0.0
        klearn_conf.feature_file = 'live_order_pay_query.txt'
        klearn_conf.item_filter = 'page_id_filter:10014@11014,direct_and_photo_live_filter_compatible,standard_live_played_started_filter_compatible'
        klearn_conf.kafka_tag = ['page_id_filter:10014@11014']
        klearn_conf.extractor_type = 'KlearnCofeaSampleExtractor'
        klearn_conf.main_task_id = '2'
        klearn_conf.eps = 1e-08
        klearn_conf.feature_text = './kai_feature.txt'
        klearn_conf.tab = ''
        klearn_conf.batch_size = 128
        klearn_conf.klearn_conf = type('',(),{})()
        klearn_conf.batch_size = 128
        klearn_conf.model_name = 'default_model_name'
        from kai.tensorflow.config.ad_config.ktrain.klearn_utils.klearn_config import parse_klearn_feature
        parse_klearn_feature(klearn_conf)
        user_conf = klearn_conf
        self._config = klearn_conf
        self._klearn_ops = klearn_ops
        self._dnn_net_size = [int(x) for x in self._config.dnn_net_size.split(',') if len(x) > 0]
        self._share_num = self._config.share_num
        self.max_paycnt = 4.0
        self._last_result = {}
        self.set_t = True
        if self.set_t:
            self.use_bn = False
            self.ln = True
            self.trainable_task_weight = True
        else:
            self.use_bn = True
            self.ln = False
            self.trainable_task_weight = True
        self.base_lr = self._config.base_lr
        self._use_w0 = False
        self._use_w1 = False
        self._use_w2 = False
        self._use_w1_2 = False

        #模型指标监控
        self.hook_inputs = {}
        self.local_step = 0
        self.pay_label_mean_acc = 0.0
        self.paycnt_label_mean_acc = 0.0
        self.paycnt_predict2_mean_acc = 0.0
   
    def get_learning_rate(self, total_train_step, cur_step, base_lr=0.007):
        if cur_step < total_train_step:
            lr = base_lr * (1.1 - math.exp(-cur_step * 2.33 / total_train_step))
            if lr > base_lr:
                lr = base_lr
            return lr
        return base_lr
    def GetWriteTensor(self):
        return self.hook_inputs
    def write_hook(self, res):
        self.pay_label_mean_acc += res['pay_label_mean']
        self.paycnt_label_mean_acc += res['paycnt_label_mean']
        self.paycnt_predict2_mean_acc += res['paycnt_predict2_mean']
        if self.local_step % 10 == 0:
            print('================= debug_info local_step {} =================\n'.format(self.local_step))
            keys = list(self.GetWriteTensor().keys())
            if 'loss' in res:
                keys.append('loss')
            for key in keys:
                if key not in res:
                    continue
                print(key + ':', res[key])
            print('pay_label_mean_acc' + ':', self.pay_label_mean_acc / self.local_step)
            print('paycnt_label_mean_acc' + ':', self.paycnt_label_mean_acc / self.local_step)
            print('paycnt_predict2_mean_acc' + ':', self.paycnt_predict2_mean_acc / self.local_step)
        self.local_step += 1
    def GetMetric(self):
        return self.eval_targets
    def GetPlaceHolder(self):
        return {"is_train": self.is_train_pl}
    def GetAUC(self):
        return {"auc": self.auc}
    def kai_v2_model_def(self):
        from kai.tensorflow.nn import ParamAttr

        is_cpu_ps = False 
        is_train = True 
        hooks = []
        exclude_dense = []
        default_param_attr = ParamAttr(initializer=kai.nn.UniformInitializer(0.01),
                                      access_method=kai.nn.ProbabilityAccess(100),
                                      recycle_method=kai.nn.UnseendaysRecycle(3650000, 0.0, False))
        kai.nn.set_default_param_attr(default_param_attr)
        sparse = kai.new_embedding('sparse', dim=16, slots=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264], expand=None)
        ecom_merchant =  kai.new_embedding('ecom_merchant', dim=16, slots=[278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294], expand=None)
        ecom_v2 = kai.new_embedding('query_static', dim=16, slots=[295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322], expand=None)
        lsim = kai.new_embedding('lsim', dim=16, slots=list(range(323, 339)), expand=None)
        top_fea = kai.new_embedding('top_fea', dim=16, slots=list(range(339, 355)), expand=None)
        pay_ability = kai.new_embedding('pay_ability', dim=16, slots=list(range(355, 364)), expand=None)
        mix_sparse = kai.new_embedding('mix_sparse', dim=16, slots=list(range(364, 370)), expand=None)
        ExtractUserVisionFeatureCoverCTRImp = kai.get_dense_fea('ExtractUserVisionFeatureCoverCTRImp', dim=128)
        ExtractUserRecallTargetInfo = kai.get_dense_fea('ExtractUserRecallTargetInfo', dim=16)
        ExtractUserAdLpsNumExtendEcomDense = kai.get_dense_fea('ExtractUserAdLpsNumExtendEcomDense', dim=2)
        ExtractUserAdItemClickNumExtendEcomDense = kai.get_dense_fea('ExtractUserAdItemClickNumExtendEcomDense', dim=2)
        ExtractRecoSlideFmRe = kai.get_dense_fea('ExtractRecoSlideFmRe', dim=128)
        ExtractUserTextFeatureBertClick = kai.get_dense_fea('ExtractUserTextFeatureBertClick', dim=32)
        ExtractPhotoDspEmbedding = kai.get_dense_fea('ExtractPhotoDspEmbedding', dim=64)
        ExtractPhotoCpaBid = kai.get_dense_fea('ExtractPhotoCpaBid', dim=1)
        ExtractPhotoVisionFeatureCoverCTR = kai.get_dense_fea('ExtractPhotoVisionFeatureCoverCTR', dim=32)
        ExtractPhotoXdtMmuEmbedding = kai.get_dense_fea('ExtractPhotoXdtMmuEmbedding', dim=128)
        ExtractPhotoTextFeatureBertEmb = kai.get_dense_fea('ExtractPhotoTextFeatureBertEmb', dim=64)
        ExtractURealTimeMerchantOrderPaiedMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedMatchDense', dim=24)
        ExtractURealTimeMerchantOrderSubmitMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderSubmitMatchDense', dim=24)
        ExtractURealTimeMerchantGoodsViewMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantGoodsViewMatchDense', dim=24)
        ExtractURealTimeMerchantShopClickMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantShopClickMatchDense', dim=24)
        ExtractURealTimeMerchantOrderSubmitClickMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderSubmitClickMatchDense', dim=24)
        ExtractURealTimeMerchantProductBuyClickMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantProductBuyClickMatchDense', dim=24)
        ExtractURealTimeMerchantOrderImpressionMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderImpressionMatchDense', dim=24)
        ExtractDenseCombineQueryAd = kai.get_dense_fea('ExtractDenseCombineQueryAd', dim=54)
        ExtractURealTimeMerchantOrderPaiedWholePayStatDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedWholePayStatDense', dim=16)
        ExtractCombineRealtimeNewAdItemClickMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdItemClickMatchCnt', dim=32)
        ExtractCombineUserLiveSimActionMatchCntFix = kai.get_dense_fea('ExtractCombineUserLiveSimActionMatchCntFix', dim=128)
        ExtractCombineUserLiveSimActionMatchCntEcommN5 = kai.get_dense_fea('ExtractCombineUserLiveSimActionMatchCntEcommN5', dim=128)
        ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt', dim=32)
        # QCPX特征：优惠券金额（单独存在，不进入共享层）
        ExtractPhotoQcpxCouponAmt = kai.get_dense_fea('ExtractPhotoQcpxCouponAmt', dim=1)
        dense = tf.concat([ExtractUserVisionFeatureCoverCTRImp, ExtractUserRecallTargetInfo, ExtractUserAdLpsNumExtendEcomDense, ExtractUserAdItemClickNumExtendEcomDense, ExtractRecoSlideFmRe, ExtractUserTextFeatureBertClick, ExtractPhotoDspEmbedding, ExtractPhotoCpaBid, ExtractPhotoVisionFeatureCoverCTR, ExtractPhotoXdtMmuEmbedding, ExtractPhotoTextFeatureBertEmb], axis=1)
        dense_v2 = tf.concat([ExtractURealTimeMerchantOrderPaiedMatchDense, ExtractURealTimeMerchantOrderSubmitMatchDense, ExtractURealTimeMerchantGoodsViewMatchDense, ExtractURealTimeMerchantShopClickMatchDense, ExtractURealTimeMerchantOrderSubmitClickMatchDense, ExtractURealTimeMerchantProductBuyClickMatchDense, ExtractURealTimeMerchantOrderImpressionMatchDense], axis=1)
        dense_query_ad_combine = ExtractDenseCombineQueryAd
        dense_top = tf.concat([ExtractURealTimeMerchantOrderPaiedWholePayStatDense, ExtractCombineRealtimeNewAdItemClickMatchCnt, ExtractCombineUserLiveSimActionMatchCntFix, ExtractCombineUserLiveSimActionMatchCntEcommN5, ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt, ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt], axis=1)
        dnn_input = tf.concat([sparse, dense, ecom_merchant, ecom_v2, dense_v2, lsim, dense_query_ad_combine, top_fea, dense_top, pay_ability,mix_sparse], axis=1)
        dnn_input_list = [sparse, dense, ecom_merchant, ecom_v2, dense_v2, lsim, dense_query_ad_combine, top_fea, dense_top, pay_ability,mix_sparse, ExtractPhotoQcpxCouponAmt]
        labels = kai.get_label('label', from_sparse=True)
        labels = tf.cast(labels, tf.int32)
        #下面的block_data、 non_block_data只是为了完成兼容，用户可以自行修改
        self.block_data = [type('', (), dict(output=input))() for input in dnn_input_list]
        self.non_block_data = type('', (), dict(label_pl=labels))()
        train_loss = self.inference(dnn_input, labels, is_cpu_ps, is_train, hooks, exclude_dense)
        sparse_optimizer = kai.optimizer.AdagradW(learning_rate=0.1, eps=1e-08, decay=0.0, l2=0.0, version=2)
        dense_optimizer = kai.optimizer.AdagradW(learning_rate=0.1, eps=1e-08, decay=0.0, l2=0.0, version=2)
        #为了兼容kai python，sparse_loss乘上batch_size * worker_num；dense_loss乘上batch_size；
        sparse_optimizer.minimize(train_loss * tf.cast(tf.shape(labels)[0], tf.float32) * kai.worker_num(), var_list=kai.get_collection(kai.GraphKeys.EMBEDDING_INPUT))
        dense_optimizer.minimize(train_loss * tf.cast(tf.shape(labels)[0], tf.float32), var_list=kai.get_collection(kai.GraphKeys.TRAINABLE_VARIABLES))
        return {'optimizer': [sparse_optimizer, dense_optimizer], 'metrics': self.eval_targets}

    def inference(self, dnn_input, labels, is_cpu_ps, is_train, hooks, exclude_dense):
        self.is_train_pl = kai.get_train_placeholder()
        sparse_input = self.block_data[0].output
        dense_input = self.block_data[1].output
        ecom_merchant_input = self.block_data[2].output
        ecom_v2_input = self.block_data[3].output
        dense_v2_input = self.block_data[4].output
        lsim_input = self.block_data[5].output
        dense_query_ad_combine = self.block_data[6].output
        top_fea = self.block_data[7].output
        dense_top = self.block_data[8].output
        pay_ability = self.block_data[9].output
        mix_sparse = self.block_data[10].output
        coupon_amt_input = self.block_data[11].output  # QCPX优惠券金额特征
     
        dnn_input = tf.concat([sparse_input, dense_input , ecom_merchant_input, ecom_v2_input, dense_v2_input, lsim_input], axis=1)
        # input
        if self.use_bn:
            logger.info('bn in the embedding layer')
            dnn_input = kai.batch_norm(dnn_input, 'bn')
        if self.ln:
            logger.info('ln in the embedding layer')
            dnn_input = tf.contrib.layers.layer_norm(dnn_input)
            query_ad_input = tf.contrib.layers.layer_norm(dense_query_ad_combine, scope="ln_query_ad_combine")
            top_fea_input = tf.contrib.layers.layer_norm(top_fea, scope="ln_top_fea")
            dense_top_input = tf.contrib.layers.layer_norm(dense_top, scope="ln_dense_top")
            pay_fea_input = tf.contrib.layers.layer_norm(pay_ability, scope="ln_pay_fea")
            mix_sparse_input = tf.contrib.layers.layer_norm(mix_sparse, scope="ln_mix_fea")
        

        masked_fea = (coupon_amt_input) * 0.0
        input = tf.concat([dnn_input, query_ad_input, top_fea_input, dense_top_input, pay_fea_input,mix_sparse_input, masked_fea], axis=1)
        input_size = input.get_shape().as_list()[1]
        logger.info('tt_debug_input:{}'.format(input))
        labels = tf.Print(labels, [labels], message='raw labels: ', first_n=100, summarize=1000)
        labels = tf.reshape(labels, [-1])
        print("labels ", labels)
        #order_paid_cnt_label = tf.bitwise.right_shift(tf.bitwise.bitwise_and(labels, 0x1F00), 8)
        gmv_label = get_label_by_bit(labels, 0, 15)
        gmv_label = tf.cast(gmv_label, tf.float32)
        bins_path = "input_bins_gmv_uniform_7.txt"
        bins, bins_center_value = load_bins(bins_path)
        gmv_label = tf.clip_by_value(gmv_label, 0.0, bins_center_value[-1] + 500.)
        gmv_int_label = tf.cast(math_ops._bucketize(gmv_label, boundaries=bins), tf.int32)
        order_paid_cnt_label = get_label_by_bit(labels, 23, 5)
        order_paid_cnt_label = tf.cast(order_paid_cnt_label, dtype=tf.float32)
        order_paid_cnt_label = tf.Print(order_paid_cnt_label, [order_paid_cnt_label],
                                        message='order_paid_cnt_label: ', first_n=100, summarize=2000)
        label_follow = get_label_by_bit(labels, 20, 1)
        label_gs = get_label_by_bit(labels, 21, 1)
        label_pay = get_label_by_bit(labels, 22, 1)
        label_live_shop_cart_click = get_label_by_bit(labels, 16, 1)
        label_live_shop_link_jump = get_label_by_bit(labels, 17, 1)
        ones_tensor = tf.ones_like(order_paid_cnt_label)
        second = ones_tensor * self.max_paycnt
        order_paid_cnt_all_label = tf.where(tf.logical_and(tf.equal(order_paid_cnt_label, 0.0), label_pay > 0),
                                            x=ones_tensor, y=order_paid_cnt_label)
        order_paid_cnt_all_label = tf.where(order_paid_cnt_all_label >= self.max_paycnt, x=second, y=order_paid_cnt_all_label)
        order_paid_cnt_all_label = tf.Print(order_paid_cnt_all_label, [order_paid_cnt_all_label], message='order_paid_cnt_all_label: ', first_n=100, summarize=1000)
        #n_samples = tf.reduce_sum(sample_weight)


        def weighted_cross_entropy_with_logits(labels, logits, pos_weight, name=None):
            labels = tf.reshape(tf.cast(labels, tf.float32), [-1, 1])
            logits = tf.reshape(tf.cast(logits, tf.float32), [-1, 1])
            pos_weight = tf.reshape(tf.cast(pos_weight, tf.float32), [-1, 1])
            z = tf.log1p(tf.exp(-1 * tf.abs(logits))) + tf.nn.relu(-logits)
            return tf.add((pos_weight * labels + 1) * z, logits, name=name)



        # mtl: share_bottom
        for i in range(self._share_num):
            with tf.variable_scope("share_bottom_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                weight_name = 'w'
                w = data_ops.variable_on_cpu(weight_name, [input_size, layer_size],
                                             initializer=tf.random_normal_initializer(
                                                 stddev=1.0 / math.sqrt(float(input_size))),
                                             is_cpu_ps=is_cpu_ps)
                bias_name = 'b'
                b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
                logger.info("%s length=%d * %d" % (w.name, input_size, layer_size))
                logger.info("%s length=%d" % (b.name, layer_size))
                o1 = tf.add(tf.matmul(input, w), b)
                if i != len(self._dnn_net_size) - 1:
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                        #layer_norm = tf.keras.layers.LayerNormalization(name = "LayerNorm")
                        #o1 = layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o = o1
                input_size = layer_size
                input = o
        # initialize
        paycnt_input = follow_input = gs_input = cart_click_input = link_jump_input = gmv_input = input
        paycnt_input_size = follow_input_size = gs_input_input_size = cart_click_input_size = link_jump_input_size = gmv_input_size = input_size
        # paycnt mtl head
        paycnt_net_size = [512, 512, 512, 512, 512, 512, 5]
        print("paycnt_net_size", paycnt_net_size)
        for i in range(self._share_num, len(paycnt_net_size)):
            with tf.variable_scope("paycnt_upper_layer_{}".format(i)):
                layer_size = paycnt_net_size[i]
                w = get_variable_on_cpu("w_%d" % i, [paycnt_input_size, layer_size], is_cpu_ps)
                b = get_variable_on_cpu("b_%d" % i, [layer_size], is_cpu_ps, initializer=tf.zeros_initializer)
                logger.info("%s length=%d * %d" % (w.name, paycnt_input_size, layer_size))
                logger.info("%s length=%d" % (b.name, layer_size))
                if i != len(paycnt_net_size) - 1:
                    o1 = tf.add(tf.add(tf.matmul(paycnt_input, w), b), paycnt_input)
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                        #layer_norm = tf.keras.layers.LayerNormalization(name = "LayerNorm")
                        #o1 = layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o1 = tf.add(tf.matmul(paycnt_input, w), b)
                    o = o1
                paycnt_input_size = layer_size
                paycnt_input = o
        # follow mtl head
        for i in range(self._share_num, len(self._dnn_net_size)):
            with tf.variable_scope("follow_upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                weight_name = 'w'
                w = data_ops.variable_on_cpu(weight_name, [follow_input_size, layer_size],
                                             initializer=tf.random_normal_initializer(
                                                 stddev=1.0 / math.sqrt(float(follow_input_size))),
                                             is_cpu_ps=is_cpu_ps)
                bias_name = 'b'
                b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
                logger.info("%s length=%d * %d" % (w.name, follow_input_size, layer_size))
                logger.info("%s length=%d" % (b.name, layer_size))
                if i != len(self._dnn_net_size) - 1:
                    o1 = tf.add(tf.add(tf.matmul(follow_input, w), b), follow_input)
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                        #layer_norm = tf.keras.layers.LayerNormalization(name = "LayerNorm")
                        #o1 = layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o1 = tf.add(tf.matmul(follow_input, w), b)
                    o = o1
                follow_input_size = layer_size
                follow_input = o
        # mtl: gs head
        for i in range(self._share_num, len(self._dnn_net_size)):
            with tf.variable_scope("gs_upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                weight_name = 'w'
                w = data_ops.variable_on_cpu(weight_name, [gs_input_input_size, layer_size],
                                             initializer=tf.random_normal_initializer(
                                                 stddev=1.0 / math.sqrt(float(gs_input_input_size))),
                                             is_cpu_ps=is_cpu_ps)
                bias_name = 'b'
                b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
                if i != len(self._dnn_net_size) - 1:
                    o1 = tf.add(tf.add(tf.matmul(gs_input, w), b), gs_input)
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                        #layer_norm = tf.keras.layers.LayerNormalization(name = "LayerNorm")
                        #o1 = layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o1 = tf.add(tf.matmul(gs_input, w), b)
                    o = o1
                gs_input_input_size = layer_size
                gs_input = o
        #  mtl: cart_click head
        for i in range(self._share_num, len(self._dnn_net_size)):
            with tf.variable_scope("cart_click_upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                weight_name = 'w'
                w = data_ops.variable_on_cpu(weight_name, [cart_click_input_size, layer_size],
                                             initializer=tf.random_normal_initializer(
                                                 stddev=1.0 / math.sqrt(float(cart_click_input_size))),
                                             is_cpu_ps=is_cpu_ps)
                bias_name = 'b'
                b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
                if i != len(self._dnn_net_size) - 1:
                    o1 = tf.add(tf.add(tf.matmul(cart_click_input, w), b), cart_click_input)
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                        #layer_norm = tf.keras.layers.LayerNormalization(name = "LayerNorm")
                        #o1 = layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o1 = tf.add(tf.matmul(cart_click_input, w), b)
                    o = o1
                cart_click_input_size = layer_size
                cart_click_input = o
        # mtl: link_jump head
        for i in range(self._share_num, len(self._dnn_net_size)):
            with tf.variable_scope("link_jump_upper_layer_{}".format(i)):
                layer_size = self._dnn_net_size[i]
                weight_name = 'w'
                w = data_ops.variable_on_cpu(weight_name, [link_jump_input_size, layer_size],
                                             initializer=tf.random_normal_initializer(
                                                 stddev=1.0 / math.sqrt(float(link_jump_input_size))),
                                             is_cpu_ps=is_cpu_ps)
                bias_name = 'b'
                b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
                if i != len(self._dnn_net_size) - 1:
                    o1 = tf.add(tf.add(tf.matmul(link_jump_input, w), b), link_jump_input)
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                        #layer_norm = tf.keras.layers.LayerNormalization(name = "LayerNorm")
                        #o1 = layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o1 = tf.add(tf.matmul(link_jump_input, w), b)
                    o = o1
                link_jump_input_size = layer_size
                link_jump_input = o

        # mtl: gmv head
        gmv_net_size = [512, 512, 512, 512, 512, 512, 7]
        for i in range(self._share_num, len(gmv_net_size)):
            with tf.variable_scope("gmv_upper_layer_{}".format(i)):
                layer_size = gmv_net_size[i]
                w = data_ops.variable_on_cpu(weight_name, [gmv_input_size, layer_size],
                                             initializer=tf.random_normal_initializer(
                                                 stddev=1.0 / math.sqrt(float(gmv_input_size))),
                                             is_cpu_ps=is_cpu_ps)
                bias_name = 'b'
                b = data_ops.variable_on_cpu(bias_name, [layer_size],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
                if i != len(gmv_net_size) - 1:
                    o1 = tf.add(tf.add(tf.matmul(gmv_input, w), b), gmv_input)
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                        #layer_norm = tf.keras.layers.LayerNormalization(name = "LayerNorm")
                        #o1 = layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o1 = tf.add(tf.matmul(gmv_input, w), b)
                    o = o1
                gmv_input_size = layer_size
                gmv_input = o
        # prob
        follow_prob = tf.nn.softmax(follow_input, name="follow_softmax")
        gs_prob = tf.nn.softmax(gs_input, name="gs_softmax")
        paycnt_prob = tf.nn.softmax(paycnt_input, name="paycnt_softmax")
        paycnt_predict2 = tf.matmul(paycnt_prob, [[0.0], [1.0], [2.0], [3.0], [4.0]],name="paycnt_predict_expect")


        paycnt_predict_logit = tf.log(tf.clip_by_value(paycnt_predict2, 1e-5, 4.0))
        paycnt_predict_logit = tf.reshape(paycnt_predict_logit, [-1,1])

        # QCPX (优惠券价格弹性) 建模  ######################################################

        dnn_net_size = [512, 512, 512, 512, 512, 512, 2]
        
        def fc_net(input_tensor, input_size, start_ind, end_ind, net_size, task_name):
            """等效于qcpx.py中的fc_net方法"""
            current_input = input_tensor
            current_input_size = input_size
            
            for i in range(start_ind, end_ind + 1):
                with tf.variable_scope("{}_{}".format(task_name, i)):
                    layer_size = net_size[i]
                    weight_name = 'w'
                    if task_name == 'paycnt_elastic_c0_layer_end' or task_name == 'paycnt_elastic_c1_layer_end':
                        weight_name = 'w_{}'.format(i)
                    w = get_variable_on_cpu(weight_name, [current_input_size, layer_size], is_cpu_ps,
                                          initializer=tf.random_normal_initializer(stddev=1.0 / math.sqrt(float(current_input_size))))
                    bias_name = 'b'
                    if task_name == 'paycnt_elastic_c0_layer_end' or task_name == 'paycnt_elastic_c1_layer_end':
                        bias_name = 'b_{}'.format(i)
                    b = get_variable_on_cpu(bias_name, [layer_size], is_cpu_ps, initializer=tf.zeros_initializer)
                    o1 = tf.add(tf.matmul(current_input, w), b)
                    if i != len(net_size) - 1:
                        if self.ln:
                            o1 = tf.contrib.layers.layer_norm(o1)
                        o = tf.nn.relu(o1)
                    else:
                        o = o1
                    current_input_size = layer_size
                    current_input = o
            return current_input
        
        # 弹性建模网络
        elastic_c0 = fc_net(input, input_size, 1, 6, dnn_net_size, 'paycnt_elastic_c0_layer_end')
        elastic_c1 = fc_net(input, input_size, 1, 6, dnn_net_size, 'paycnt_elastic_c1_layer_end')

        # 从elastic_c1的输出中提取第2个值（索引为1）
        # 从elastic_c0的输出中提取第2个值（索引为1）
        # 结果形状变成[batch_size, 1]
        c1 = tf.math.tanh(tf.slice(elastic_c1, [0, 1], [-1, 1]) / 5.0)
        c0 = tf.math.tanh(tf.slice(elastic_c0, [0, 1], [-1, 1]) / 5.0)
        c1 = tf.identity(c1, name='elastic_c1')
        c0 = tf.identity(c0, name='elastic_c0')
        
        # 优惠券金额特征 - 与qcpx.py保持一致
        # coupon_amt_fea = tf.expand_dims(coupon_amt_input / 1000.0 / 10.0, axis=1)  # [batch_size, 1]
        coupon_amt_fea = coupon_amt_input / 1000.0 / 10.0
        vec_coupon = tf.concat([tf.ones_like(coupon_amt_fea), coupon_amt_fea], axis=1)  # [batch_size, 2]
        coefs = tf.concat([c0, c1], axis=1) 
        coefs = tf.reshape(coefs, [-1, 2]) # [batch_size, 2]
        
        qcpx_logit = tf.clip_by_value(tf.reduce_sum(tf.multiply(vec_coupon, coefs), axis=1), -5.0, 5.0)  # [batch_size]
        qcpx_logit = tf.reshape(qcpx_logit, [-1, 1])  # 调整形状以匹配后续计算
        ############################################################################



        # final_logit = paycnt_predict_logit + qcpx_logit
        final_logit = tf.add_n([tf.stop_gradient(paycnt_predict_logit), qcpx_logit])
        paycnt_predict2  = tf.exp(tf.clip_by_value(final_logit, -10, 10))



        cart_click_prob = tf.nn.softmax(cart_click_input, name="cart_click_softmax")
        link_jump_prob = tf.nn.softmax(link_jump_input, name="link_jump_softmax")
        gmv_prob = tf.nn.softmax(gmv_input, name="gmv_softmax")
        bins_center_value = np.array(bins_center_value).reshape(7, 1).astype(np.float32)
        gmv_out = tf.matmul(tf.cast(gmv_prob, tf.float32), tf.cast(bins_center_value, tf.float32), name="gmv_output")
        # loss
        follow_cross_entropy = tf.reduce_mean(tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_follow,
            logits=follow_input), name='follow_xentropy')
        gs_cross_entropy = tf.reduce_mean(tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_gs,
            logits=gs_input), name='gs_xentropy')
        paycnt_cross_entropy = tf.reduce_mean(tf.nn.sparse_softmax_cross_entropy_with_logits(labels=tf.cast(order_paid_cnt_all_label, tf.int32),
                                                                      logits=paycnt_input),
            name="paycnt_xentropy")
        cart_click_cross_entropy = tf.reduce_mean(
            tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_live_shop_cart_click,
                                                           logits=cart_click_input),
            name='cart_click_xentropy')
        link_jump_cross_entropy = tf.reduce_mean(
            tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_live_shop_link_jump,
                                                           logits=link_jump_input),
            name='link_jump_xentropy')
        gmv_cross_entropy = tf.reduce_mean(tf.nn.sparse_softmax_cross_entropy_with_logits(labels=tf.reshape(gmv_int_label,[-1]),
                                                           logits=gmv_input),
            name='gmv_xentropy')

        batch_size = tf.cast(tf.shape(labels)[0], tf.float32)
        paycnt_wce_loss = tf.divide(
            tf.reduce_sum(weighted_cross_entropy_with_logits(
                labels=label_pay,
                logits=final_logit,
                pos_weight=order_paid_cnt_all_label
            )),
            batch_size + 1e-6,
            name="paycnt_wce_loss"
        )

      

        
        if self.trainable_task_weight:
            logger.info("use trainable_task_weight")
            w_follow = data_ops.variable_on_cpu('w_follow', [1],
                                                initializer=tf.zeros_initializer,
                                                is_cpu_ps=is_cpu_ps)
            w_gs = data_ops.variable_on_cpu('w_gs', [1],
                                            initializer=tf.zeros_initializer,
                                            is_cpu_ps=is_cpu_ps)
            w_paycnt = data_ops.variable_on_cpu('w_paycnt', [1],
                                             initializer=tf.zeros_initializer,
                                             is_cpu_ps=is_cpu_ps)
            w_cart_click = data_ops.variable_on_cpu('w_cart_click', [1],
                                                    initializer=tf.zeros_initializer,
                                                    is_cpu_ps=is_cpu_ps)
            w_link_jump = data_ops.variable_on_cpu('w_link_jump', [1],
                                                   initializer=tf.zeros_initializer,
                                                   is_cpu_ps=is_cpu_ps)
            w_gmv = data_ops.variable_on_cpu('w_gmv', [1],
                                                   initializer=tf.zeros_initializer,
                                                   is_cpu_ps=is_cpu_ps)
            w_paycnt_wce = data_ops.variable_on_cpu('w_paycnt_wce', [1],
                                                   initializer=tf.zeros_initializer,
                                                   is_cpu_ps=is_cpu_ps)
                                                
        if self._use_w0:
            logger.info("total loss only use follow")
            train_loss = follow_cross_entropy
        elif self._use_w1:
            logger.info("total loss use goods view")
            train_loss = gs_cross_entropy
        elif self._use_w2:
            logger.info("total loss use order paid")
            train_loss = paycnt_cross_entropy
        elif self._use_w1_2:
            logger.info("total loss use goods view and order paid")
            train_loss = tf.add(gs_cross_entropy * tf.exp(-w_gs) + 0.5*w_gs, paycnt_cross_entropy * tf.exp(-w_paycnt) + 0.5*w_paycnt, name = 'loss_weighted_sum')
        else:
            logger.info("total loss use all.")
            # train_loss = tf.add(paycnt_cross_entropy * tf.exp(-w_paycnt) + 0.5 * w_paycnt +
            #                     follow_cross_entropy * tf.exp(-w_follow) + 0.5*w_follow,
            #                     gs_cross_entropy * tf.exp(-w_gs) + 0.5*w_gs +
            #                     gmv_cross_entropy * tf.exp(-w_gmv) + 0.5*w_gmv +
            #                     cart_click_cross_entropy * tf.exp(-w_cart_click) + 0.5*w_cart_click +
            #                     link_jump_cross_entropy * tf.exp(-w_link_jump) + 0.5*w_link_jump,
            #                     name='loss_weighted_sum')
            train_loss = tf.add_n([
                paycnt_cross_entropy * tf.exp(-w_paycnt) + 0.5 * w_paycnt,
                paycnt_wce_loss * tf.exp(-w_paycnt_wce) + 0.5 * w_paycnt_wce,
                follow_cross_entropy * tf.exp(-w_follow) + 0.5 * w_follow,
                gs_cross_entropy * tf.exp(-w_gs) + 0.5 * w_gs,
                cart_click_cross_entropy * tf.exp(-w_cart_click) + 0.5 * w_cart_click,
                link_jump_cross_entropy * tf.exp(-w_link_jump) + 0.5 * w_link_jump,
                gmv_cross_entropy * tf.exp(-w_gmv) + 0.5 * w_gmv
            ], name='loss_weighted_sum_advanced')

        tf.summary.scalar("train_loss", tf.reshape(train_loss, shape=[]))
        
        # log
        follow_prob_mean = tf.reduce_mean(follow_prob[:, 1], name="follow_prob_mean")
        gs_prob_mean = tf.reduce_mean(gs_prob[:, 1], name="gs_prob_mean")
        paycnt_predict2_mean = tf.reduce_mean(paycnt_predict2, name="paycnt_predict2_mean")
        cart_click_prob_mean = tf.reduce_mean(cart_click_prob[:, 1], name="cart_click_prob_mean")
        link_jump_prob_mean = tf.reduce_mean(link_jump_prob[:, 1], name="link_jump_prob_mean")
        gmv_prob_mean = tf.reduce_mean(gmv_out, name="gmv_prob_mean")
        follow_label_mean = tf.reduce_mean(tf.cast(label_follow, tf.float32), name="follow_real_mean")
        gs_label_mean = tf.reduce_mean(tf.cast(label_gs, tf.float32), name="gs_real_mean")
        pay_label_mean = tf.reduce_mean(tf.cast(label_pay, tf.float32), name="pay_real_mean")
        paycnt_label_mean = tf.reduce_mean(tf.cast(order_paid_cnt_all_label, tf.float32), name="paycnt_real_mean")
        cart_click_label_mean = tf.reduce_mean(tf.cast(label_live_shop_cart_click, tf.float32),
                                               name="cart_click_real_mean")
        link_jump_label_mean = tf.reduce_mean(tf.cast(label_live_shop_link_jump, tf.float32),
                                              name="link_jump_real_mean")
        gmv_label_mean = tf.reduce_mean(tf.cast(gmv_label, tf.float32),
                                              name="gmv_real_mean")
        self.prob = tf.clip_by_value(paycnt_predict2, tf.constant(0.0), tf.constant(1.0))
        self.label = tf.cast(label_pay, tf.float32)
        with tf.device('/cpu:0'):
            _, self.auc, reset_auc_eval = auc_eval(self.label, self.prob)
        self.eval_targets = [("live_order_pay_auc", self.prob, self.label,
                              tf.cast(tf.ones_like(paycnt_predict2), tf.float32), "auc")]
        self.hook_inputs['train_loss'] = train_loss
        self.hook_inputs['pay_label_mean'] = pay_label_mean
        self.hook_inputs['paycnt_label_mean'] = paycnt_label_mean
        self.hook_inputs['paycnt_predict2_mean'] = paycnt_predict2_mean
        self.hook_inputs['auc'] = self.auc
        self.hook_inputs['link_jump_label_mean'] = link_jump_label_mean
        self.hook_inputs['link_jump_prob_mean'] = link_jump_prob_mean
        self.hook_inputs['gmv_label_mean'] = gmv_label_mean
        self.hook_inputs['gmv_prob_mean'] = gmv_prob_mean
        return train_loss

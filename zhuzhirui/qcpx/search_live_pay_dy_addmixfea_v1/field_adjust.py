filename = "feature_map_for_ps_old.txt"
new_file_name = "feature_map_for_ps.txt"
#filename = "kai_feature_old.txt"
#new_file_name = "kai_feature.txt"
seen = set()
w = open(new_file_name, 'w')
index = 0
dense_index = 0
with open(filename) as f:
    for line in f:
        # if line[0] == '#':
        #    continue
        if (line.startswith("class") == False and line.startswith("remap_slot") == False) or line[0] == '#':
            w.write(line)
            continue
        flds = line.strip().split(",")
        if len(flds) <= 1:
            continue
        rr = []
        category = ""
        classname = ""
        for fld in flds:
            ff = fld.strip().split("=")
            ff = [f.strip() for f in ff]
            if ff[1].startswith("dense"):
                index = dense_index
                dense_index+=1
            if ff[0] == "field":
                # rr.append("field={}".format(ff[1]))
                rr.append("field={}".format(str(index)))
                index += 1
            else:
                rr.append("{}={}".format(ff[0], ff[1]))

        new_line = ", ".join(rr) + "\n"
        w.write(new_line)
w.close()
f.close()


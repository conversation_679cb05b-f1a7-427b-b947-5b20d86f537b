import kai.tensorflow as kai
MODEL_TRANS_ORIGIN='python'
import tensorflow as tf
import sys
import math
import logging
import numpy as np
import operator

LOG_FORMAT = "%(asctime)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s] - %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)
logger = logging.getLogger(__name__)
from kai.tensorflow.config.ad_config.ktrain.AUC import auc as auc_eval
from kai.tensorflow.config.ad_config.ktrain.utils import metric_merge
from lr_schedule_hook import LearningRateStepDecayHook
from lr_schedule_hook import my_load_dense_func


def variables_summary(var_name, var):
    var_name = var_name.replace(":0", "")
    var = tf.cast(var, tf.float32)
    with tf.name_scope(var_name):
        var_norm = tf.norm(var)
        var_max = tf.reduce_max(var)
        var_min = tf.reduce_min(var)
        var_mean = tf.reduce_mean(var)
        tf.summary.scalar(var_name + "_norm", var_norm)
        tf.summary.scalar(var_name + "_max", var_max)
        tf.summary.scalar(var_name + "_min", var_min)
        tf.summary.scalar(var_name + "_mean", var_mean)
        tf.summary.histogram(var_name, var)


def variables_mean_summary(var_name, var):
    var_name = var_name.replace(":0", "")
    var = tf.cast(var, tf.float32)
    with tf.name_scope(var_name):
        var_mean = tf.reduce_mean(var)
        tf.summary.scalar(var_name + "_mean", var_mean)


def get_label_by_bit(labels, bit, num):
    val = 2 ** num - 1
    # (labels >> bit) & val
    return tf.cast(tf.bitwise.bitwise_and(tf.bitwise.right_shift(labels, bit), val), tf.int32)


class UserModel(object):
    def __init__(self):
        conf = type('', (), {})()
        self.conf = conf
        self.sparse_user_slots = [1, 2, 3, 4, 5, 6, 7, 8, 9, 707, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 291, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 708, 59, 676, 61, 62, 63, 64, 65, 66, 67, 709, 69, 70, 71, 72, 73, 74, 75, 76, 77, 677, 79, 80, 81, 82, 83, 84, 85, 678, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 679, 106, 107, 108, 680, 681, 682, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 683, 684, 685, 128, 129, 130, 131, 132, 133, 134, 686, 687, 688, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 689, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 292, 293, 294, 710, 296, 711, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 627, 628, 629, 630, 631, 695, 696, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 703, 704, 705, 258, 259, 706, 267, 268, 269, 270, 271]
        self.sparse_item_slots = [697, 698, 699, 700, 701, 702, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 690, 198, 199, 200, 201, 723, 203, 691, 205, 692, 207, 208, 209, 210, 693, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 274, 251, 252, 253, 254, 273, 290, 694, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 650, 671, 651, 652, 653, 654, 655, 656, 672, 673, 657, 674, 658, 659, 660, 661, 662, 663, 664, 675, 665, 666, 667, 668, 669, 670, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 729, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 623, 624, 625, 626, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349]
        self.sparse_rebase_slots = [712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 724, 725, 726, 727, 728]

    def GetPlaceHolder(self):
        return {"is_train": self.is_train_pl}

    def fc_net(self, input, input_size, start_ind, end_ind, dnn_net_size, task_name):
        for i in range(start_ind, end_ind + 1):
            with tf.variable_scope("{}_{}".format(task_name, i)):
                layer_size = dnn_net_size[i]
                weight_name = 'w'
                if task_name == 'paycnt_upper_layer' or task_name == 'paycnt_upper_layer_end':
                    weight_name = 'w_{}'.format(i)
                w = tf.get_variable(weight_name, [input_size, layer_size],
                                    initializer=tf.random_normal_initializer(
                                        stddev=1.0 / math.sqrt(float(input_size))),
                                    trainable=True)
                bias_name = 'b'
                if task_name == 'paycnt_upper_layer' or task_name == 'paycnt_upper_layer_end':
                    bias_name = 'b_{}'.format(i)
                b = tf.get_variable(bias_name, [layer_size],
                                    initializer=tf.zeros_initializer,
                                    trainable=True)
                logger.info("%s length=%d * %d" % (w.name, input_size, layer_size))
                logger.info("%s length=%d" % (b.name, layer_size))
                o1 = tf.add(tf.matmul(input, w), b)
                if task_name == 'share_bottom_layer' or i != len(dnn_net_size) - 1:
                    if self.use_bn:
                        o1 = kai.batch_norm(o1, 'bn')
                    if self.ln:
                        o1 = tf.contrib.layers.layer_norm(o1)
                    o = tf.nn.relu(o1)
                else:
                    o = o1
                input_size = layer_size
                input = o
        return input

    def fc_layer(self, dnn_input, input_size, output_size, task, is_output_layer, rz=False):
        with tf.variable_scope("layer_{}".format(task)):
            weight_name = 'w'
            w = tf.get_variable(weight_name, [input_size, output_size],
                                initializer=tf.random_normal_initializer(stddev=1.0 / math.sqrt(float(input_size))),
                                trainable=True)
            bias_name = 'b'
            b = tf.get_variable(bias_name, [output_size],
                                initializer=tf.zeros_initializer,
                                trainable=True)
            logger.info("task:{}, {} length={}*{}".format(task, w.name, input_size, output_size))
            logger.info("task:{}, {} length={}".format(task, b.name, output_size))
            o1 = tf.add(tf.matmul(dnn_input, w), b)
            if not is_output_layer:
                if rz and input_size == output_size:
                    logger.info("task:{} use rezero".format(task))
                    alpha = tf.get_variable("alpha", [1], initializer=tf.ones_initializer, )
                    o = tf.nn.relu(tf.add(tf.multiply(o1, alpha), dnn_input))
                else:
                    o = tf.nn.relu(o1)
                if self.ln:
                    logger.info("task:{} use ln".format(task))
                    o = tf.contrib.layers.layer_norm(o)
                if self.use_bn:
                    logger.info("task:{} use bn".format(task))
                    o = kai.batch_norm(o, 'bn')
            else:
                o = o1
        return o

    def se_net(self, sparse_input, dense_input, task):
        sparse_units = sparse_input.get_shape()[-1]
        n_sparse_fields = sparse_units // 16
        sparse_input_reshape = tf.reshape(sparse_input, [-1, n_sparse_fields, 16])
        sparse_input_squeeze = tf.reduce_mean(sparse_input_reshape, 2)
        excite_size = 62
        inout_size = [(n_sparse_fields, excite_size), (excite_size, n_sparse_fields)]
        for i in range(len(inout_size)):
            tmp_task = task + '_excitation_' + str(i)
            sparse_input_squeeze = self.fc_layer(sparse_input_squeeze, inout_size[i][0], inout_size[i][1], tmp_task, i == 1)
        importances = tf.sigmoid(sparse_input_squeeze, name='importances')
        
        for i in range(n_sparse_fields):
            field_imp = tf.slice(importances, [0, i], [-1, 1])
            variables_mean_summary("field_{}_imp".format(i), field_imp)

        expanded_importances = tf.reshape(importances, [-1, n_sparse_fields, 1], name='expanded_importances')
        excitation = tf.reshape(tf.multiply(sparse_input_reshape, expanded_importances), [-1, sparse_units], name='excitation')
        dnn_input = tf.concat([excitation, dense_input], -1)
        return dnn_input
    
    def cgc_layer(self, dnn_input, tasks, experts, units):
        input_size = dnn_input.get_shape().as_list()[1]

        # shared experts
        # experts * (batch_size, units, 1)
        shared_expert_outputs = []
        for i in range(experts):
            w = tf.get_variable('shared_expert_{}_w'.format(i), [input_size, units],
                                initializer=tf.random_normal_initializer(stddev=1.0 / math.sqrt(float(input_size))),
                                trainable=True)
            b = tf.get_variable('shared_expert_{}_b'.format(i), [units],
                                initializer=tf.zeros_initializer,
                                trainable=True)
            expert_output = tf.reshape(tf.add(tf.matmul(dnn_input, w), b), [-1, units, 1])
            expert_output = tf.nn.relu(tf.contrib.layers.layer_norm(expert_output))
            shared_expert_outputs.append(expert_output)

        # specialized expert
        # tasks * (batch_size, units, 1)
        specialized_expert_outputs = []
        for i in range(tasks):
            w = tf.get_variable('specialized_expert_{}_w'.format(i), [input_size, units],
                                initializer=tf.random_normal_initializer(stddev=1.0 / math.sqrt(float(input_size))),
                                trainable=True)
            b = tf.get_variable('specialized_expert_{}_b'.format(i), [units],
                                initializer=tf.zeros_initializer,
                                trainable=True)
            expert_output = tf.reshape(tf.add(tf.matmul(dnn_input, w), b), [-1, units, 1])
            expert_output = tf.nn.relu(tf.contrib.layers.layer_norm(expert_output))
            specialized_expert_outputs.append(expert_output)
        
        # tasks * (batch_size, units)
        gate_outputs = []
        for i in range(tasks):
            expert_outputs = tf.concat(shared_expert_outputs + [specialized_expert_outputs[i]], axis=-1)
            gate_w = tf.get_variable('gate_{}_w'.format(i), [input_size, experts + 1],
                                        initializer=tf.random_normal_initializer(stddev=1.0 / math.sqrt(float(input_size))),
                                        trainable=True)
            gate_b = tf.get_variable('gate_{}_b'.format(i), [experts + 1],
                                        initializer=tf.zeros_initializer,
                                        trainable=True)
            gate_output = tf.add(tf.matmul(dnn_input, gate_w), gate_b)
            gate_output = tf.reshape(tf.nn.softmax(gate_output, axis=-1), [-1, 1, experts + 1])

            for j in range(experts + 1):
                expert_weight = tf.slice(gate_output, [0, 0, j], [-1, 1, 1])
                variables_mean_summary("task_{}_expert_{}_imp".format(i, j), expert_weight)

            weight_expert_output = tf.reduce_sum(tf.multiply(expert_outputs, gate_output), axis=-1)
            gate_outputs.append(weight_expert_output)

        return gate_outputs
    
    def weighted_cross_entropy_with_logits(self, labels, logits, pos_weight, name=None):
        labels = tf.reshape(tf.cast(labels, tf.float32), [-1, 1])
        logits = tf.reshape(tf.cast(logits, tf.float32), [-1, 1])
        pos_weight = tf.reshape(tf.cast(pos_weight, tf.float32), [-1, 1])
        z = tf.log1p(tf.exp(-1 * tf.abs(logits))) + tf.nn.relu(-logits)
        return tf.add((pos_weight * labels + 1) * z, logits, name = name)

    def kai_v2_model_def(self):
        from kai.tensorflow.nn import ParamAttr

        is_cpu_ps = False 
        is_train = True 
        hooks = []
        exclude_dense = []
        default_param_attr = ParamAttr(initializer=kai.nn.UniformInitializer(0.01),
                                      access_method=kai.nn.ProbabilityAccess(100),
                                      recycle_method=kai.nn.UnseendaysRecycle(3650000, 0.0, False))
        kai.nn.set_default_param_attr(default_param_attr)

        # sparse user
        sparse_user = kai.new_embedding('sparse_user', dim=16, slots=self.sparse_user_slots, expand=None)
        
        # sparse item
        sparse_item = kai.new_embedding('sparse_item', dim=16, slots=self.sparse_item_slots, expand=None)

        # sparse rebase
        sparse_rebase = kai.new_embedding('sparse_rebase', dim=16, slots=self.sparse_rebase_slots, expand=None)

        ExtractUserVisionFeatureCoverCTRImp__0 = kai.get_dense_fea('ExtractUserVisionFeatureCoverCTRImp', dim=128)
        ExtractUserRecallTargetInfo__0 = kai.get_dense_fea('ExtractUserRecallTargetInfo', dim=16)
        ExtractUserAdLpsNumExtendEcomDense__0 = kai.get_dense_fea('ExtractUserAdLpsNumExtendEcomDense', dim=2)
        ExtractUserAdItemClickNumExtendEcomDense__0 = kai.get_dense_fea('ExtractUserAdItemClickNumExtendEcomDense', dim=2)
        ExtractRecoSlideFmRe__0 = kai.get_dense_fea('ExtractRecoSlideFmRe', dim=128)
        ExtractUserTextFeatureBertClick__0 = kai.get_dense_fea('ExtractUserTextFeatureBertClick', dim=32)
        ExtractURealTimeMerchantOrderPaiedWholePayStatDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedWholePayStatDense', dim=16)
        ExtractURealTimeMerchantOrderPaiedPhotoPayStatDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedPhotoPayStatDense', dim=16)
        ExtractURealTimeMerchantOrderPaiedLivePayStatDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedLivePayStatDense', dim=16)
        ExtractUserMerchantStatPleDense__0 = kai.get_dense_fea('ExtractUserMerchantStatPleDense', dim=48)
        ExtractPhotoDspEmbedding__0 = kai.get_dense_fea('ExtractPhotoDspEmbedding', dim=64)
        ExtractPhotoVisionFeatureCoverCTR__0 = kai.get_dense_fea('ExtractPhotoVisionFeatureCoverCTR', dim=32)
        ExtractPhotoXdtMmuEmbedding__0 = kai.get_dense_fea('ExtractPhotoXdtMmuEmbedding', dim=128)
        ExtractPhotoTextFeatureBertEmb__0 = kai.get_dense_fea('ExtractPhotoTextFeatureBertEmb', dim=64)
        ExtractAuthorMerchantStatPleDense__0 = kai.get_dense_fea('ExtractAuthorMerchantStatPleDense', dim=50)
        ExtractCombineUserLiveSimActionMatchCntFix__0 = kai.get_dense_fea('ExtractCombineUserLiveSimActionMatchCntFix', dim=128)
        ExtractCombineUserLiveSimActionMatchCntEcommN5__0 = kai.get_dense_fea('ExtractCombineUserLiveSimActionMatchCntEcommN5', dim=128)
        ExtractCombineRealtimeNewAdPhotoPlayed3sMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewAdPhotoPlayed3sMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdLiveShopClickMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewAdLiveShopClickMatchCnt', dim=32)
        ExtractCombineRealtimeNewEventGoodsViewMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewEventGoodsViewMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdItemClickMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewAdItemClickMatchCnt', dim=32)
        ExtractCombineRealtimeNewEventOrderPaidMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewEventOrderPaidMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdItemImpressionMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewAdItemImpressionMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt__0 = kai.get_dense_fea('ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt', dim=32)
        ExtractCombineUserRealtimeActionMatchDspLiveImp__0 = kai.get_dense_fea('ExtractCombineUserRealtimeActionMatchDspLiveImp', dim=16)
        ExtractCombineUserRealtimeActionMatchFansTopLiveImp__0 = kai.get_dense_fea('ExtractCombineUserRealtimeActionMatchFansTopLiveImp', dim=16)
        ExtractCombineUserRealtimeActionMatchDspLps__0 = kai.get_dense_fea('ExtractCombineUserRealtimeActionMatchDspLps', dim=16)
        ExtractCombineUserRealtimeActionMatchFansTopLps__0 = kai.get_dense_fea('ExtractCombineUserRealtimeActionMatchFansTopLps', dim=16)
        ExtractURealTimeMerchantOrderPaiedMatchDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedMatchDense', dim=24)
        ExtractURealTimeMerchantOrderSubmitMatchDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantOrderSubmitMatchDense', dim=24)
        ExtractURealTimeMerchantGoodsViewMatchDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantGoodsViewMatchDense', dim=24)
        ExtractURealTimeMerchantShopClickMatchDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantShopClickMatchDense', dim=24)
        ExtractURealTimeMerchantOrderSubmitClickMatchDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantOrderSubmitClickMatchDense', dim=24)
        ExtractURealTimeMerchantProductBuyClickMatchDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantProductBuyClickMatchDense', dim=24)
        ExtractURealTimeMerchantOrderImpressionMatchDense__0 = kai.get_dense_fea('ExtractURealTimeMerchantOrderImpressionMatchDense', dim=24)
        ExtractUMerchantAuthorMatchDense__0 = kai.get_dense_fea('ExtractUMerchantAuthorMatchDense', dim=6)
        ExtractUMerchantAuthorMatchRatioDense__0 = kai.get_dense_fea('ExtractUMerchantAuthorMatchRatioDense', dim=6)
        ExtractULongTermMerchantAuthorIdMatchDense__0 = kai.get_dense_fea('ExtractULongTermMerchantAuthorIdMatchDense', dim=30)
        ExtractRealTimeCombineOrderPaiedExplainingAttrMatchDense__0 = kai.get_dense_fea('ExtractRealTimeCombineOrderPaiedExplainingAttrMatchDense', dim=5)
        ExtractRealTimeCombineGoodsViewExplainingAttrMatchDense__0 = kai.get_dense_fea('ExtractRealTimeCombineGoodsViewExplainingAttrMatchDense', dim=5)
        ExtractLongTermCombineOrderPaiedExplainingAttrMatchDense__0 = kai.get_dense_fea('ExtractLongTermCombineOrderPaiedExplainingAttrMatchDense', dim=5)
        ExtractLongTermCombineGoodsViewExplainingAttrMatchDense__0 = kai.get_dense_fea('ExtractLongTermCombineGoodsViewExplainingAttrMatchDense', dim=5)
        ExtractAddCartAuthorItemMatchCntDense__0 = kai.get_dense_fea('ExtractAddCartAuthorItemMatchCntDense', dim=24)
        ExtractUpdateCartAuthorItemMatchCntDense__0 = kai.get_dense_fea('ExtractUpdateCartAuthorItemMatchCntDense', dim=24)
        ExtractDeleteCartAuthorItemMatchCntDense__0 = kai.get_dense_fea('ExtractDeleteCartAuthorItemMatchCntDense', dim=24)
        ExtractImMessageAuthorMatchCntDense__0 = kai.get_dense_fea('ExtractImMessageAuthorMatchCntDense', dim=8)
        ExtractRefundAuthorMatchCntDense__0 = kai.get_dense_fea('ExtractRefundAuthorMatchCntDense', dim=8)
        ExtractMixRankEcomRecoContextMatchTargetDense__0 = kai.get_dense_fea('ExtractMixRankEcomRecoContextMatchTargetDense', dim=4)

        dense__0 = tf.concat([ExtractAddCartAuthorItemMatchCntDense__0, ExtractUpdateCartAuthorItemMatchCntDense__0, ExtractDeleteCartAuthorItemMatchCntDense__0, ExtractImMessageAuthorMatchCntDense__0, ExtractRefundAuthorMatchCntDense__0, ExtractMixRankEcomRecoContextMatchTargetDense__0, ExtractUserVisionFeatureCoverCTRImp__0, ExtractUserRecallTargetInfo__0, ExtractUserAdLpsNumExtendEcomDense__0, ExtractUserAdItemClickNumExtendEcomDense__0, ExtractRecoSlideFmRe__0, ExtractUserTextFeatureBertClick__0, ExtractURealTimeMerchantOrderPaiedWholePayStatDense__0, ExtractURealTimeMerchantOrderPaiedPhotoPayStatDense__0, ExtractURealTimeMerchantOrderPaiedLivePayStatDense__0, ExtractUserMerchantStatPleDense__0, ExtractPhotoDspEmbedding__0, ExtractPhotoVisionFeatureCoverCTR__0, ExtractPhotoXdtMmuEmbedding__0, ExtractPhotoTextFeatureBertEmb__0, ExtractAuthorMerchantStatPleDense__0, ExtractCombineUserLiveSimActionMatchCntFix__0, ExtractCombineUserLiveSimActionMatchCntEcommN5__0, ExtractCombineRealtimeNewAdPhotoPlayed3sMatchCnt__0, ExtractCombineRealtimeNewAdLiveShopClickMatchCnt__0, ExtractCombineRealtimeNewEventGoodsViewMatchCnt__0, ExtractCombineRealtimeNewAdItemClickMatchCnt__0, ExtractCombineRealtimeNewEventOrderPaidMatchCnt__0, ExtractCombineRealtimeNewAdItemImpressionMatchCnt__0, ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt__0, ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt__0, ExtractCombineUserRealtimeActionMatchDspLiveImp__0, ExtractCombineUserRealtimeActionMatchFansTopLiveImp__0, ExtractCombineUserRealtimeActionMatchDspLps__0, ExtractCombineUserRealtimeActionMatchFansTopLps__0, ExtractURealTimeMerchantOrderPaiedMatchDense__0, ExtractURealTimeMerchantOrderSubmitMatchDense__0, ExtractURealTimeMerchantGoodsViewMatchDense__0, ExtractURealTimeMerchantShopClickMatchDense__0, ExtractURealTimeMerchantOrderSubmitClickMatchDense__0, ExtractURealTimeMerchantProductBuyClickMatchDense__0, ExtractURealTimeMerchantOrderImpressionMatchDense__0, ExtractUMerchantAuthorMatchDense__0, ExtractUMerchantAuthorMatchRatioDense__0, ExtractULongTermMerchantAuthorIdMatchDense__0, ExtractRealTimeCombineOrderPaiedExplainingAttrMatchDense__0, ExtractRealTimeCombineGoodsViewExplainingAttrMatchDense__0, ExtractLongTermCombineOrderPaiedExplainingAttrMatchDense__0, ExtractLongTermCombineGoodsViewExplainingAttrMatchDense__0], axis=1)
        
        # add coupoun info and p_cvr info
        ExtractItemDenseUnifyCvr = kai.get_dense_fea('ExtractItemDenseUnifyCvr', dim=1)
        ExtractDenseEspQcpxOriginPcvr = kai.get_dense_fea('ExtractDenseEspQcpxOriginPcvr', dim=1)
        ExtractPhotoQcpxCouponAmt = kai.get_dense_fea('ExtractPhotoQcpxCouponAmt', dim=1)
        ExtractPhotoQcpxCouponThreshold = kai.get_dense_fea('ExtractPhotoQcpxCouponThreshold', dim=1)
        dense__1 = tf.concat([ExtractItemDenseUnifyCvr,
                            ExtractDenseEspQcpxOriginPcvr,
                            ExtractPhotoQcpxCouponAmt,
                            ExtractPhotoQcpxCouponThreshold], axis=1)

        item_type = kai.get_dense_fea('ExtractItemSourceDense', dim=7)
        dnn_input = [sparse_user, sparse_item, sparse_rebase, dense__0, dense__1, item_type]
        labels = kai.get_dense_fea('048863cab4df52018726dc179a66179d', dim=1, dtype=tf.int64)
        labels = tf.cast(labels, tf.int32)

        block_data = [type('', (), dict(output=input))() for input in dnn_input]
        non_block_data = type('', (), dict(label_pl=labels))()
        metric, eval_targets, prediction, _ = self.model_def(block_data, non_block_data)

        sparse_optimizer = kai.optimizer.AdagradW(learning_rate=0.2, eps=1e-08, decay=0.0, l2=0.0, version=2)
        dense_optimizer = kai.optimizer.AdagradW(learning_rate=0.2, eps=1e-08, decay=0.0, l2=0.0, version=2)

        # lr_schedule_hook = LearningRateStepDecayHook(0.2, 20000)
        # kai.add_run_hook(lr_schedule_hook, "lr_schedule_hook")
        # kai.set_load_dense_func(my_load_dense_func)

        sparse_optimizer.minimize(metric['loss'], var_list=kai.get_collection(kai.GraphKeys.EMBEDDING_INPUT))
        dense_optimizer.minimize(metric['loss'] / kai.worker_num(), var_list=kai.get_collection(kai.GraphKeys.TRAINABLE_VARIABLES))
        return {'optimizer': [sparse_optimizer, dense_optimizer], 'metrics': eval_targets}

    def model_def(self, block_data, non_block_data):
        # for labels
        labels = non_block_data.label_pl

        labels = tf.reshape(tf.cast(labels, tf.int32), [-1])
        mix_label = get_label_by_bit(labels, 0, 1)
        variables_summary("mix_label", mix_label)

                
        label_pay = get_label_by_bit(labels, 3, 1)  # 目前前后链路只有一个字段，后链路数据时会覆盖度前链路写的
        variables_summary("label_pay", label_pay)
        label_pay_front = tf.boolean_mask(label_pay, tf.equal(mix_label, 0))

        ones_tensor = tf.ones_like(labels)
        zeros_tensor = tf.zeros_like(labels)

        item_source = block_data[5].output
        item_type = tf.reshape(tf.slice(item_source, [0, 0], [-1, 1]), [-1])
        pos_id = tf.reshape(tf.slice(item_source, [0, 3], [-1, 1]), [-1])
        ad_queue_type = tf.reshape(tf.slice(item_source, [0, 4], [-1, 1]), [-1])
        ocpc_action_type = tf.reshape(tf.slice(item_source, [0, 6], [-1, 1]), [-1])

        item_type_flag = tf.equal(item_type, 2.0)
        item_type_flag = tf.cast(item_type_flag, tf.float32) * 0.0 + 1.0
        ocpc_action_type = tf.Print(ocpc_action_type, [ocpc_action_type], message='[ocpc_action_type]: ', first_n=3, summarize=200)
        pay_flag = tf.cast(tf.where(tf.equal(ocpc_action_type, 395), ones_tensor, zeros_tensor), tf.float32)
        roas_flag = tf.cast(tf.where(tf.equal(ocpc_action_type, 395),zeros_tensor, ones_tensor ), tf.float32)
        order_paid_cnt_label = get_label_by_bit(labels, 4, 5)
        self.order_paid_cnt_label = tf.cast(order_paid_cnt_label, tf.float32)
        order_paid_cnt_label = tf.where(tf.logical_and(tf.equal(order_paid_cnt_label, 0), label_pay > 0),
                                        x=ones_tensor, y=order_paid_cnt_label)
        order_paid_label = tf.where(tf.greater(order_paid_cnt_label, 0), ones_tensor, zeros_tensor)

        self.real_cvr_label = tf.cast(tf.where(tf.logical_and(tf.equal(order_paid_cnt_label, 0), label_pay > 0),
                                        x=ones_tensor, y=label_pay), tf.float32)
        
        order_paid_cnt_label_for_front = tf.cast(tf.clip_by_value(order_paid_cnt_label, 0, 10), tf.float32)
        order_paid_cnt_label_for_front = order_paid_cnt_label_for_front * item_type_flag
        
        order_paid_cnt_label_front1 = get_label_by_bit(labels, 9, 5)
        order_paid_cnt_label_end = tf.subtract(order_paid_cnt_label, order_paid_cnt_label_front1)
        order_paid_cnt_label_end = tf.cast(tf.clip_by_value(order_paid_cnt_label_end, 0, 10), tf.float32)
        order_paid_cnt_label_end = order_paid_cnt_label_end * item_type_flag
        
        order_paid_cnt_label = tf.cast(tf.clip_by_value(order_paid_cnt_label, 0, 10), tf.float32) * item_type_flag
        
        label_pay_end = tf.where(tf.greater(order_paid_cnt_label_end, 0), ones_tensor, zeros_tensor)
        label_pay_end = tf.boolean_mask(label_pay_end, tf.equal(mix_label, 1))
        order_paid_cnt_label_end = tf.boolean_mask(order_paid_cnt_label_end, tf.equal(mix_label, 1),
                                                   name='order_paid_cnt_label_end')

        order_paid_cnt_label_front = tf.boolean_mask(order_paid_cnt_label_for_front, tf.equal(mix_label, 0),
                                                    name='order_paid_cnt_label_front')

        front_size = tf.cast(tf.size(order_paid_cnt_label_front), dtype=tf.int64)
        end_size = tf.cast(tf.size(order_paid_cnt_label_end), dtype=tf.int64)
        ad_size = tf.cast(front_size + end_size, dtype=tf.int64)
        print("front_size_shape:", front_size)
        print("end_size_shape:", end_size)
        print("ad_size_shape", ad_size)
    
        # for features
        sparse_user_input = block_data[0].output
        block_data[0].output
        logger.info("sparse_user_input shape: {}".format(sparse_user_input.get_shape()))
        sparse_item_input = block_data[1].output
        logger.info("sparse_item_input shape: {}".format(sparse_item_input.get_shape()))
        sparse_rebase_input = block_data[2].output
        logger.info("sparse_rebase_input shape: {}".format(sparse_rebase_input.get_shape()))
        dense_input = block_data[3].output
        logger.info("dense_input shape: {}".format(dense_input.get_shape()))
        dense_input_qcpx = block_data[4].output
        logger.info("dense_input_qcpx shape: {}".format(dense_input_qcpx.get_shape()))
        
        # unify cvr
        pred_ori_cvr = tf.cast(tf.reshape(tf.slice(dense_input_qcpx, [0, 0], [-1, 1]), [-1]), tf.float32)
        pred_ori_cvr_1 = tf.cast(tf.reshape(tf.slice(dense_input_qcpx, [0, 1], [-1, 1]), [-1]), tf.float32)
        coupon_amt = tf.cast(tf.reshape(tf.slice(dense_input_qcpx, [0, 2], [-1, 1]), [-1]), tf.float32)
        coupon_threshold = tf.cast(tf.reshape(tf.slice(dense_input_qcpx, [0, 3], [-1, 1]), [-1]), tf.float32)

        variables_summary("coupon_amt", coupon_amt)
        # variables_summary("coupoun_threshold", coupoun_threshold)

        ori_cvr_pred = tf.clip_by_value(pred_ori_cvr, 0.000000305902227, 0.9999996941)
        self.ori_cvr_pred_logit = tf.log(ori_cvr_pred)
        # self.ori_cvr_pred_front = tf.boolean_mask(ori_cvr_pred, tf.equal(mix_label, 0))
        # self.ori_cvr_pred_end = tf.boolean_mask(ori_cvr_pred, tf.equal(mix_label, 1))
        # self.ori_cvr_pred_logit = tf.math.log(ori_cvr_pred) - tf.math.log(1 - ori_cvr_pred)
        # self.ori_cvr_pred_front_logit = tf.boolean_mask(self.ori_cvr_pred_logit, tf.equal(mix_label, 0))
        # self.ori_cvr_pred_end_logit = tf.boolean_mask(self.ori_cvr_pred_logit, tf.equal(mix_label, 1))
        emb_size = 16
        n_sparse_fields = 436
        sparse_units = n_sparse_fields * emb_size
        dense_units = 1640
        
        dnn_input = tf.concat([sparse_user_input, sparse_item_input, sparse_rebase_input, dense_input], axis=1)
        logger.info("dnn_input shape: {}".format(dnn_input.get_shape()))

        # for net
        self.is_train_pl = kai.get_train_placeholder()
        dnn_net_size = [512, 512, 512, 512, 512, 512, 2]
        self.use_bn = False
        self.ln = True
        self.trainable_task_weight = False

 
        dnn_input = tf.contrib.layers.layer_norm(dnn_input, scope="LayerNorm")
        sparse_input = tf.slice(dnn_input, [0, 0], [-1, sparse_units])
        dense_input = tf.slice(dnn_input, [0, sparse_units], [-1, dense_units]) 

        with tf.variable_scope('senet_layer'):
            input = self.se_net(sparse_input, dense_input, 'senet')
            input_size = input.get_shape()[-1]

        bias_input = tf.stop_gradient(input, name='bias_input')
        bias_input_size = bias_input.get_shape().as_list()[-1]

        bias_input = self.fc_net(bias_input, bias_input_size, 0, 1, [128, input_size], 'bias_layer')
        bias_input = tf.sigmoid(bias_input)
        input = bias_input * input
        input_size = input.get_shape().as_list()[-1]
        qcpx_logit = self.fc_net(input , input_size, 1, 6, dnn_net_size,
                                          'paycnt_upper_layer')
        variables_summary("qcpx_logit",tf.slice(qcpx_logit, [0, 1], [-1, 1])) 
        qcpx_logit =  tf.clip_by_value(tf.slice(qcpx_logit, [0, 1], [-1, 1]), -3,3)
        # elastic_c1 = self.fc_net(input, input_size, 1, 6, dnn_net_size,
        #                                 'paycnt_elastic_c1_layer_end')
        # variables_summary("uplift_elastic_c0",tf.slice(elastic_c0, [0, 1], [-1, 1]))
        # variables_summary("uplift_elastic_c1",tf.slice(elastic_c1, [0, 1], [-1, 1]) )

        # 弹性建模
        # c1 = tf.math.tanh(tf.slice(elastic_c1, [0, 1], [-1, 1]) / 5.0)
        # c0 = tf.math.tanh(tf.slice(elastic_c0, [0, 1], [-1, 1]) / 5.0)
        # c1 = tf.identity(c1, name='elastic_c1')
        # c0 = tf.identity(c0, name='elastic_c0')
        # variables_summary("c1", c1)
        # variables_summary("c0", c0)

        # 优惠券金额特征
        coupon_amt_fea = tf.expand_dims(coupon_amt / 1000.0 / 10.0, axis=1)  # [b,1]
        # vec_coupon = tf.concat([tf.ones_like(coupon_amt_fea), coupon_amt_fea], axis=1)  # [b,2]
        # coefs = tf.concat([c0, c1], axis=1)  # [b,2]
        # qcpx_logit = tf.clip_by_value(tf.reduce_sum(tf.multiply(vec_coupon, coefs), axis=1), -5.0, 5.0)  # [b]
        # main_logit = tf.clip_by_value(self.ori_cvr_pred_logit, -15.0, 15.0)
        main_logit = tf.reshape(self.ori_cvr_pred_logit, [-1,1])
        paycnt_output =  tf.add_n([tf.stop_gradient(main_logit), qcpx_logit])

        
        paycnt_prob = tf.sigmoid(paycnt_output, name="paycnt_sigmoid")
        paycnt_predict = tf.divide(paycnt_prob, 1.0 - paycnt_prob, name = 'paycnt_predict')
        
        paycnt_predict_mean = tf.reduce_mean(paycnt_predict, name="paycnt_predict_mean")
        pay_label_mean = tf.reduce_mean(tf.cast(self.real_cvr_label, tf.float32), name="pay_real_mean")
        paycnt_label_mean = tf.reduce_mean(tf.cast(order_paid_cnt_label, tf.float32),
                                                 name="paycnt_front_real_mean")
        ori_cvr_pred_mean = tf.reduce_mean(tf.cast(ori_cvr_pred, tf.float32), name="ori_cvr_pred_mean")
        # check pcoc
        # 整体
        tf.summary.scalar("paycnt_predict_mean", paycnt_predict_mean)
        tf.summary.scalar("paycnt_label_mean", paycnt_label_mean)
        tf.summary.scalar("ori_cvr_pred_mean", ori_cvr_pred_mean)
        tf.summary.scalar("pay_label_mean", pay_label_mean)

        # for loss
        paycnt_cross_entropy = tf.divide(tf.reduce_sum(self.weighted_cross_entropy_with_logits(
            self.real_cvr_label, paycnt_output, order_paid_cnt_label, name='loss_paycnt_wcel')),
            tf.cast(ad_size, tf.float32) + 1e-6, name='paycnt_xentropy')

        if self.trainable_task_weight:
            logger.info("use trainable_task_weight")
            w_paycnt = tf.get_variable('w_paycnt', [1],
                                       initializer=tf.zeros_initializer)
            variables_summary("w_paycnt", w_paycnt)
        else:
            w_paycnt = tf.constant(1.0, dtype=tf.float32)
        train_loss = paycnt_cross_entropy

        # logit summury
        variables_summary("ori_cvr_pred_logit", self.ori_cvr_pred_logit)
        variables_summary("paycnt_output", paycnt_output)
        variables_summary("paycnt_predict", paycnt_predict)

        # loss summury
        tf.summary.scalar(f"paycnt_cross_entropy", paycnt_cross_entropy)
        tf.summary.scalar(f"train_loss", tf.squeeze(train_loss))

        self.label = tf.clip_by_value(self.real_cvr_label, tf.constant(0.0), tf.constant(1.0))
        self.prob = tf.clip_by_value(tf.cast(paycnt_predict, tf.float32), tf.constant(0.0), tf.constant(1.0))
        self.paycnt_predict = tf.cast(tf.clip_by_value(paycnt_predict[:, 0], tf.constant(0.0), tf.constant(1.0)), tf.float32)
        with tf.device('/cpu:0'):
            _, self.paycnt_front_auc, reset_auc_eval = auc_eval(self.label, self.paycnt_predict)
            self.auc = self.paycnt_front_auc
        
        #原始cvr值
        ori_wei = tf.ones([1024])
        self.ori_cvr_pred_mean = tf.fill([1024], ori_cvr_pred_mean)
        self.paycnt_label_mean = tf.fill([1024], paycnt_label_mean)
        self.paycnt_predict_mean = tf.fill([1024], paycnt_predict_mean)

        #中间指标记录
        self.eval_targets = [
            ("uplift_pay_cnt_auc", self.paycnt_predict, self.real_cvr_label, ori_wei, "auc")]
        self.eval_targets.append(
            ("ori_pay_cnt_auc", ori_cvr_pred, self.real_cvr_label, ori_wei, "auc"))
        self.eval_targets.append(
            ("ori_pay_reg", ori_cvr_pred, self.order_paid_cnt_label, ori_wei , "linear_regression"))
        self.eval_targets.append(
            ("uplift_pay_reg", self.paycnt_predict, self.order_paid_cnt_label, ori_wei , "linear_regression"))
        targets, prediction = metric_merge(self.eval_targets)

        return {
                   "loss": train_loss,
                   "targets": targets,
                   "auc": self.auc,
               }, self.eval_targets, prediction, self.GetPlaceHolder()